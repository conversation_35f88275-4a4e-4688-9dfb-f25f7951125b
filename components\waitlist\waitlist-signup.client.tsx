// components/waitlist-signup.tsx
import { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import useClickOutside from '../../hooks/useClickOutside';

import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../ui/buttons/input';
import { Check, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { StudentIcon, TeacherIcon, SchoolIcon, EmailIcon, SearchIcon } from '../ui/icons';

interface School {
  place_id: string;
  name: string;
  formatted_address: string;
  location?: { lat: number; lng: number };
  viewport?: any;
}

interface WaitlistSignupProps {
  userType: 'student' | 'teacher' | 'school_admin';
  placeholder?: string;
  submitText?: string;
  apiKey: string;
}

// Declare Google Maps types
declare global {
  interface Window {
    google: any;
    initGooglePlaces: () => void;
  }
}

// Define specific types for Google Maps Web Components
interface MapElement extends HTMLElement {
  innerMap: google.maps.Map;
  center: google.maps.LatLng | google.maps.LatLngLiteral;
  zoom: number;
  'map-id'?: string;
}

interface AdvancedMarkerElement extends HTMLElement {
  position: google.maps.LatLng | google.maps.LatLngLiteral | null;
}

// Extend JSX to include Google's web components
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'gmp-map': React.HTMLAttributes<MapElement> & { 
        ref?: React.Ref<MapElement>;
        center?: google.maps.LatLng | google.maps.LatLngLiteral;
        zoom?: number;
        'map-id'?: string;
      };
      'gmp-advanced-marker': React.HTMLAttributes<AdvancedMarkerElement> & { 
        ref?: React.Ref<AdvancedMarkerElement>; 
        position?: google.maps.LatLng | google.maps.LatLngLiteral | null;
      };
    }
  }
}

export const WaitlistSignup: React.FC<WaitlistSignupProps> = ({
  userType,
  placeholder = "Enter your email address",
  submitText = "Join Waitlist",
  apiKey
}) => {
  const [email, setEmail] = useState('');
  const [school, setSchool] = useState<School | null>(null);
  const [schoolQuery, setSchoolQuery] = useState('');
  const [schoolSuggestions, setSchoolSuggestions] = useState<School[]>([]);
  const [showSchoolSearch, setShowSchoolSearch] = useState(false);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [registeredSchoolIds, setRegisteredSchoolIds] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<'school' | 'email'>('school');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number; width: number; maxHeight: number } | null>(null);
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const [showInput, setShowInput] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showStudentError, setShowStudentError] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [googleMapsLoaded, setGoogleMapsLoaded] = useState(false);
  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral>({ lat: 56.2639, lng: 9.5018 });
  const [mapZoom, setMapZoom] = useState<number>(7);
  const [markerPosition, setMarkerPosition] = useState<google.maps.LatLngLiteral | null>(null);

  const inputRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const messageTimeoutRef = useRef<NodeJS.Timeout>();
  const mapRef = useRef<MapElement>(null);
  const markerRef = useRef<AdvancedMarkerElement>(null);
  const [predictions, setPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  // Load Google Maps API - ensure correct libraries are loaded for map components
  useEffect(() => {
    const loadGoogleMaps = async () => {
      if (window.google?.maps?.places) {
        setGoogleMapsLoaded(true);
        return;
      }

      if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        // If a script is already there but google object not ready, wait for it.
        window.initGooglePlaces = () => setGoogleMapsLoaded(true);
        if (window.google) setGoogleMapsLoaded(true);
        return;
      }

      try {
        const script = document.createElement('script');
        // The new web components load their own dependencies.
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,marker&v=beta&callback=initGooglePlaces`;
        script.async = true;

        window.initGooglePlaces = () => {
          setGoogleMapsLoaded(true);
        };

        script.onerror = () => {
          console.error('Failed to load Google Maps API');
          setGoogleMapsLoaded(false); // Fallback to backend search can be triggered
        };

        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading Google Maps:', error);
      }
    };

    if (userType === 'school_admin') {
      loadGoogleMaps();
    }
  }, [apiKey, userType]);

  // Initialize Google services once the API is loaded
  useEffect(() => {
    if (googleMapsLoaded && mapRef.current?.innerMap) {
      if (!autocompleteService.current) {
        autocompleteService.current = new google.maps.places.AutocompleteService();
      }
      if (!placesService.current) {
        placesService.current = new google.maps.places.PlacesService(mapRef.current.innerMap);
      }
    }
  }, [googleMapsLoaded, showSchoolSearch]);

  // Calculate dropdown position based on form position
  const updateDropdownPosition = useCallback(() => {
    if (formRef.current && showSchoolSearch) {
      const rect = formRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Calculate available space with proper margins
      const MARGIN = 16;
      const spaceBelow = viewportHeight - rect.bottom - MARGIN;
      const spaceAbove = rect.top - MARGIN;

      // Determine which dropdown content to use for height calculation
      const isSchoolStep = userType === 'school_admin' && currentStep === 'school';
      const activeDropdownItems = isSchoolStep ? predictions.length : schoolSuggestions.length;

      // Calculate item height (60px per item) and total needed height
      const ITEM_HEIGHT = 60;
      const PADDING = 8;
      const neededHeight = activeDropdownItems * ITEM_HEIGHT + PADDING;

      // Determine position and constrain height to available space
      let finalHeight: number;
      let showAbove = false;

      if (neededHeight <= spaceBelow) {
        // Fits below - use full needed height
        finalHeight = neededHeight;
        showAbove = false;
      } else if (neededHeight <= spaceAbove) {
        // Fits above - use full needed height
        finalHeight = neededHeight;
        showAbove = true;
      } else {
        // Doesn't fit in either space - use the larger space
        if (spaceBelow >= spaceAbove) {
          finalHeight = Math.max(120, spaceBelow); // Minimum 120px
          showAbove = false;
        } else {
          finalHeight = Math.max(120, spaceAbove); // Minimum 120px
          showAbove = true;
        }
      }

      // Ensure dropdown stays within horizontal bounds
      const leftPosition = Math.max(MARGIN, Math.min(rect.left + window.scrollX, viewportWidth - rect.width - MARGIN));

      setDropdownPosition({
        top: showAbove
          ? rect.top + window.scrollY - finalHeight - 8
          : rect.bottom + window.scrollY + 8,
        left: leftPosition,
        width: rect.width,
        maxHeight: finalHeight
      });
    }
  }, [showSchoolSearch, predictions.length, schoolSuggestions.length, userType, currentStep]);

  // Update position when school search is shown or window resizes
 useEffect(() => {
    if (showSchoolSearch) {
      updateDropdownPosition();

      const handleResize = () => updateDropdownPosition();
      const handleScroll = () => updateDropdownPosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
      };
    } else {
      setDropdownPosition(null);
    }
  }, [showSchoolSearch, updateDropdownPosition]);

  // Fetch registered schools on component mount
  useEffect(() => {
    const fetchRegisteredSchools = async () => {
      try {
        const response = await fetch('/api/registered-schools');
        if (response.ok) {
          const data = await response.json();
          setRegisteredSchoolIds(data.registeredPlaceIds || []);
        }
      } catch (error) {
        console.error('Failed to fetch registered schools:', error);
      }
    };

    fetchRegisteredSchools();
  }, []);

  // Fetch autocomplete predictions when the search query changes
  useEffect(() => {
    if (!autocompleteService.current || !schoolQuery || !showSchoolSearch) {
      setPredictions([]);
      return;
    }

    const handler = setTimeout(() => {
      autocompleteService.current?.getPlacePredictions(
        { input: schoolQuery, types: ['school'] },
        (results) => {
          setPredictions(results || []);
        }
      );
    }, 300);

    return () => clearTimeout(handler);
  }, [schoolQuery, showSchoolSearch]);

  const handlePredictionSelect = (prediction: google.maps.places.AutocompletePrediction) => {
    if (!placesService.current || !mapRef.current || !markerRef.current) return;

    placesService.current.getDetails(
      { placeId: prediction.place_id, fields: ['name', 'formatted_address', 'geometry', 'place_id'] },
      (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && place?.geometry?.location) {
          const location = place.geometry.location;
          const newPosition = { lat: location.lat(), lng: location.lng() };

          const selectedSchool: School = {
            place_id: place.place_id!,
            name: place.name!,
            formatted_address: place.formatted_address!,
            location: newPosition,
            viewport: place.geometry.viewport || null,
          };
          
          setSchool(selectedSchool);
          setSchoolQuery(selectedSchool.name); // Update input to show full name
          setPredictions([]); // Hide predictions

          // Update map state to re-center and place marker
          setMapCenter(newPosition);
          setMapZoom(17);
          setMarkerPosition(newPosition);
        }
      }
    );
  };

  // Reset form when userType changes
  useEffect(() => {
    resetForm();
  }, [userType]);

  const resetForm = useCallback(() => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }
    
    setEmail('');
    setSchool(null);
    setSchoolQuery('');
    setSchoolSuggestions([]);
    setShowSchoolSearch(false);
    setCurrentStep(userType === 'school_admin' ? 'school' : 'email');
    setShowInput(false);
    setMessage('');
    setMessageType('');
    setSuccess(false);
    setIsSubmitting(false);
    setShowStudentError(false);
    setIsResetting(false);
  }, [userType]);

  const resetFormWithDelay = useCallback(() => {
    setIsResetting(true);
    setShowInput(false);
    setShowStudentError(false);
    setShowSchoolSearch(false);
    
    setTimeout(() => {
      resetForm();
    }, 200);
  }, [resetForm]);

  // Handle click outside
  useClickOutside(formRef, () => {
    if ((showInput || showStudentError || showSchoolSearch) && !isSubmitting) {
      resetFormWithDelay();
    }
  });

  // Fallback: Backend school search (only if Google Maps fails to load)
  const searchSchoolsBackend = useCallback(async (query: string) => {
    if (query.length < 3) {
      setSchoolSuggestions([]);
      return;
    }

    setIsLoadingSchools(true);
    try {
      const response = await fetch('/api/search-schools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        console.error('Backend search failed:', response.status);
        setSchoolSuggestions([]);
        return;
      }

      const data = await response.json();
      if (data.schools) {
        setSchoolSuggestions(data.schools);
      } else {
        setSchoolSuggestions([]);
      }

    } catch (error) {
      console.error('Backend search error:', error);
      setSchoolSuggestions([]);
    } finally {
      setIsLoadingSchools(false);
    }
  }, []);

  // Debounced backend search (fallback)
  useEffect(() => {
    if (!googleMapsLoaded && showSchoolSearch) {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        if (schoolQuery) {
          searchSchoolsBackend(schoolQuery);
        }
      }, 300);

      return () => {
        if (searchTimeoutRef.current) {
          clearTimeout(searchTimeoutRef.current);
        }
      };
    }
  }, [schoolQuery, showSchoolSearch, googleMapsLoaded, searchSchoolsBackend]);

  const handleSchoolSelect = (selectedSchool: School) => {
    setSchool(selectedSchool);
    setSchoolQuery(selectedSchool.name);
    setSchoolSuggestions([]);
    setShowSchoolSearch(false);
    setCurrentStep('email');
    setShowInput(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // For students, the feature is not yet available. Just show the error state and stop.
    if (userType === 'student') {
      setShowStudentError(true);
      return;
    }

    if (userType === 'school_admin') {
      if (currentStep === 'school') {
        // If search UI isn't visible yet, show it.
        if (!showSchoolSearch) {
          setShowSchoolSearch(true);
          return;
        }
        // If a school has been selected, move to the email step.
        if (school) {
          setCurrentStep('email');
          setShowInput(true);
          return;
        }
        // Otherwise, do nothing (button should be disabled).
        return;
      }
    } else {
      // For non-school admins, just show the input on the first click.
      if (!showInput) {
        setShowInput(true);
        return;
      }
    }

    // --- The rest of the function now only deals with the final email submission --- 

    if (!email || !email.includes('@')) {
      setMessage('Please enter a valid email address');
      setMessageType('error');
      
      // Clear validation error message after 3 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
      return;
    }

    setIsSubmitting(true);
    setMessage('');
    setMessageType('');

    try {
      const response = await fetch('/api/join-waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          userType,
          name: email.split('@')[0],
          school: userType === 'school_admin' ? school : undefined,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSuccess(true);
        setMessage('You have joined the waitlist! Check your email.');
        setMessageType('success');
        
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          resetForm();
        }, 5000);
      } else {
        if (result.error === 'Email already exists') {
          setMessage('This email is already on our waitlist!');
          setMessageType('error');
        } else if (result.error === 'School already registered') {
          setMessage('This school is already registered on our waitlist!');
          setMessageType('error');
        } else {
          setMessage(result.error || 'Something went wrong. Please try again.');
          setMessageType('error');
        }
        
        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      setMessage('Please check your connection and try again.');
      setMessageType('error');
      
      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDisplayText = () => {
    // If the student error is showing, this message takes priority.
    if (showStudentError) {
      return "Student Sign In - Coming Soon!";
    }

    switch (userType) {
      case 'student':
        return "I'm a Student";
      case 'teacher':
        return 'Join our waitlist as a Teacher';
      case 'school_admin':
        if (currentStep === 'school') {
          if (school) {
            return `Selected: ${school.name}`;
          }
          return 'Search for your School';
        }
        // currentStep is 'email'
        return 'Enter your work email address';
      default:
        return 'Join Waitlist';
    }
  };

  const getSubmitText = () => {
    if (userType === 'student') {
      return 'Sign In';
    }
    if (userType === 'school_admin' && currentStep === 'school') {
      return 'Select School';
    }
    return submitText;
  };

  const isStudentMode = userType === 'student';
  const isSchoolSearchStep = userType === 'school_admin' && currentStep === 'school';
  const shouldShowEmailInput = userType === 'school_admin' ? currentStep === 'email' && showInput : showInput;

  const isSubmitDisabled = userType === 'school_admin' 
    ? (currentStep === 'school' ? !school : (!email.includes('@') || !school))
    : (!email.includes('@') || isStudentMode);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!inputRef.current) return;
    const rect = inputRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);

  return (
    <div className="relative w-full max-w-[400px] mx-auto" ref={formRef}>
      <form onSubmit={handleSubmit} className="relative">
        <InputButtonProvider
          showInput={!isStudentMode && (shouldShowEmailInput || (isSchoolSearchStep && showSchoolSearch))}
          setShowInput={isSchoolSearchStep ? setShowSchoolSearch : setShowInput}
          transition={{ type: 'spring', stiffness: 400, damping: 30 }}
          className={`relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden ${
            showStudentError
              ? 'bg-gradient-to-b from-red-50 via-red-100 to-red-200 dark:from-red-900/30 dark:via-red-800/40 dark:to-red-700/50 text-red-700 dark:text-red-300'
              : 'bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
          }`}
          onMouseMove={handleMouseMove}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          
          <InputButton>
            <AnimatePresence>
              {((!shouldShowEmailInput && !(isSchoolSearchStep && showSchoolSearch)) || showStudentError) && !isResetting && (
                <motion.div
                  key="action-text"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full h-full"
                >
                  {userType === 'student' ? (
                    <button type="submit" className="w-full h-full flex items-center justify-start text-inherit font-medium">
                      <div className="flex items-center gap-2 font-manrope_1">
                        <StudentIcon className="w-4 h-4" />
                        {getDisplayText()}
                      </div>
                    </button>
                  ) : (
                    <InputButtonAction>
                      <div className="flex items-center gap-2">
                        {userType === 'teacher' && <TeacherIcon className="w-4 h-4" />}
                        {userType === 'school_admin' && !school && <SearchIcon className="w-4 h-4" />}
                        {getDisplayText()}
                      </div>
                    </InputButtonAction>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            <InputButtonSubmit
              onClick={() => {}}
              type="submit"
              disabled={isSubmitting || (isSubmitDisabled && !showStudentError)}
              message={message}
              messageType={messageType}
              isSubmitting={isSubmitting}
              success={success}
              className={`${showStudentError ? 'opacity-50 cursor-not-allowed' : ''} disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {success ? (
                <motion.span
                  key="success"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Check />
                </motion.span>
              ) : isSubmitting ? (
                <motion.span
                  key="pending"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Loader2 className="animate-spin" />
                </motion.span>
              ) : (
                getSubmitText()
              )}
            </InputButtonSubmit>

            {(shouldShowEmailInput || (isSchoolSearchStep && showSchoolSearch)) && !isStudentMode && (
              <div className="flex items-center w-full pl-0">
                <InputButtonInput
                  type={isSchoolSearchStep ? 'text' : 'email'}
                  placeholder={isSchoolSearchStep ? 'Search school address or city' : placeholder}
                  value={isSchoolSearchStep ? schoolQuery : email}
                  onChange={isSchoolSearchStep ? (e) => setSchoolQuery(e.target.value) : (e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                  required
                  autoFocus
                />
              </div>
            )}

            <div
              ref={inputRef}
              className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
              style={{
                opacity,
                WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
              }}
            />
          </InputButton>
        </InputButtonProvider>

        {/* Autocomplete predictions dropdown - rendered as portal to avoid clipping */}
         {typeof window !== 'undefined' && dropdownPosition && isSchoolSearchStep && predictions.length > 0 &&
          createPortal(
            <AnimatePresence>
              <motion.div
                className="fixed bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-xl z-[9999]"
                style={{
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  width: `${dropdownPosition.width}px`,
                  height: `${dropdownPosition.maxHeight}px`,
                  contain: 'layout style paint',
                } as React.CSSProperties}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <div
                  className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
                  style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent',
                  } as React.CSSProperties}
                >
                  <ul className="list-none p-0 m-0">
                    {predictions.map((p) => {
                      const isDisabled = registeredSchoolIds.includes(p.place_id);
                      return (
                        <li
                          key={p.place_id}
                          onClick={() => !isDisabled && handlePredictionSelect(p)}
                          className={`px-4 py-3 text-left text-sm font-manrope_1 border-b border-zinc-100 dark:border-zinc-700 last:border-b-0 first:rounded-t-lg last:rounded-b-lg min-h-[60px] flex items-center ${
                            isDisabled
                              ? 'text-zinc-400 dark:text-zinc-500 cursor-not-allowed bg-zinc-50 dark:bg-zinc-900'
                              : 'text-black/80 dark:text-white/80 cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-700'
                          }`}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span className="flex-1 truncate">{p.description}</span>
                            {isDisabled && (
                              <span className="text-xs text-zinc-400 dark:text-zinc-500 ml-2 flex-shrink-0">Already registered</span>
                            )}
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </motion.div>
            </AnimatePresence>,
            document.body
          )
        }
      </form>

      {/* Container for Google Place Picker and Map */}
      <AnimatePresence>
        {userType === 'school_admin' && showSchoolSearch && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.3 }}
            className="absolute w-full h-full z-10 mt-2"
          >
            <div className="p-2 rounded-lg bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]">
              {googleMapsLoaded ? (
                <gmp-map
                  ref={mapRef}
                  center={mapCenter}
                  zoom={mapZoom}
                  map-id="fb0295e7ee732af67595084e"
                  style={{ height: '350px', borderRadius: '12px' }}
                >
                  <gmp-advanced-marker ref={markerRef} position={markerPosition}></gmp-advanced-marker>
                </gmp-map>
              ) : (
                <div className="flex items-center justify-center h-[250px]">
                  <Loader2 className="w-6 h-6 animate-spin text-zinc-500" />
                  <span className="ml-2 font-manrope_1 text-xs text-black/80 dark:text-white/80">Loading Map...</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Fallback school suggestions dropdown (only if Google Maps isn't loaded) - rendered as portal */}
      {typeof window !== 'undefined' && dropdownPosition && !googleMapsLoaded && showSchoolSearch && schoolSuggestions.length > 0 &&
        createPortal(
          <AnimatePresence>
            <div
                className="fixed bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-xl z-[9999]"
              style={{
                top: `${dropdownPosition.top}px`,
                left: `${dropdownPosition.left}px`,
                width: `${dropdownPosition.width}px`,
                height: `${dropdownPosition.maxHeight}px`,
                contain: 'layout style paint',
              } as React.CSSProperties}
            >
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent',
                } as React.CSSProperties}
              >
              {schoolSuggestions.map((schoolOption) => (
                <button
                  key={schoolOption.place_id}
                  type="button"
                  onClick={() => handleSchoolSelect(schoolOption)}
                  className="w-full px-4 py-3 text-left hover:bg-zinc-50 dark:hover:bg-zinc-700 border-b border-zinc-100 dark:border-zinc-700 last:border-b-0 transition-colors min-h-[60px] flex flex-col justify-center"
                >
                  <div className="font-medium text-zinc-900 dark:text-zinc-100 truncate">
                    {schoolOption.name}
                  </div>
                  <div className="text-sm text-zinc-500 dark:text-zinc-400 truncate">
                    {schoolOption.formatted_address}
                  </div>
                </button>
              ))}
              {isLoadingSchools && (
                <div className="px-4 py-3 text-center text-zinc-500 dark:text-zinc-400 min-h-[60px] flex items-center justify-center">
                  <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                  Searching schools...
                </div>
              )}
              </motion.div>
            </div>
          </AnimatePresence>,
          document.body
        )
      }

    </div>
  );
};